import time
from dataclasses import dataclass
from enum import IntEnum, Enum
import asyncio
from typing import Dict, Optional, Callable, Any, List
from functools import wraps

import bc_stark_sdk
from bc_stark_sdk.main_mod import stark
from PySide6.QtCore import QObject, Signal
from loguru import logger

from src.core import HandVersion, ProtocolType, Baudrate, V2HandVersion, FingerID, ActionSequenceId
from src.core.hand_specifications import HandType
from src.utils.async_runner import AsyncRunner


get_info_delay = 0.002


def require_hand_connected(func):
    @wraps(func)
    def wrapper(self, hand_type: HandType, *args, **kwargs):
        if hand_type not in self._hands:
            # 打印方法名
            logger.info(f"Method {func.__name__} called with hand_type {hand_type}, but hand not connected")
            self.error_occurred_signal.emit(f"{hand_type.name} hand not connected", hand_type)
            return
        return func(self, hand_type, *args, **kwargs)
    return wrapper


class HandConfig:
    def __init__(self):
        self.info_requests_pending = 0  #统计获取信息的请求数量
        self.name = ""
        self.hand_version = None
        self.connect_port = ""
        self.connect_rs485_baudrate = Baudrate.Baud460800
        self.protocol_type = ProtocolType.Modbus
        self.slave_address = 126
        self.serial_number = ""
        self.hand_type = HandType.Left
        self.sku = ""
        self.firmware_version = ""
        self.min_positions = [None] * 6
        self.max_positions = [None] * 6
        self.max_speeds = [None] * 6
        self.max_currents = [None] * 6
        self.unit_mode = None
        self.auto_calibration_enabled = True
        self.turbo_mode_enabled = False
        self.led_enabled = True
        self.buzzer_enabled = True
        self.vibration_enabled = True
        self._action_sequences = {}

    def insert_action_sequence(self, action_sequence_id: ActionSequenceId, action_sequence: stark.ActionSequence):
        self._action_sequences[action_sequence_id] = action_sequence

    def get_action_sequences(self):
        # 数据结构倒序排列， 前端再反过来
        return dict(sorted(self._action_sequences.items(), key=lambda item: item[0].value, reverse=True))

    def check_finger_params_validity(self):
        if None in self.min_positions + self.max_positions + self.max_speeds + self.max_currents:
            return False
        return True

    def track_request_start(self):
        self.info_requests_pending += 1
    
    def track_request_complete(self):
        self.info_requests_pending -= 1
        return self.info_requests_pending <= 0

    def __str__(self):
        info = ""
        info += '---------------------------------------------------------\n'
        for key, value in self.__dict__.items():
            info += f"{key}: {value}\n"
        info += '---------------------------------------------------------\n'
        return info


# 单个手设备的管理类
class HandDevice:
    _initialized: bool = False

    def __init__(self, config: HandConfig):
        self.config = config
        self.client = None
        self.connected = False
        self._sdk = bc_stark_sdk.main_mod.stark  # 每个设备单独使用一个SDK实例

        self._packing_gesture_count = 0
        self._packing_gesture_params = {0: [[int(0.4*59) * 10, 0, 0, 0, 0, 0], [500] * 6],
                                        1: [[int(0.4*59) * 10, 0, 0, 0, 0, 0], [500] * 6],
                                        2: [[45 * 10, 50, 810, 810, 810, 810], [1000] * 6]}

    async def initialize_sdk(self):
        try:

            if not HandDevice._initialized:
                if self.config.hand_version == HandVersion.V2:
                    self._sdk.init_config(V2HandVersion.Basic.to_sdk_type(), self.config.protocol_type.to_sdk_type())
                    HandDevice._initialized = True
                    logger.info(
                        f"设备SDK配置初始化成功: {self.config.hand_type.name}手, 版本:{self.config.hand_version}, 协议:{self.config.protocol_type}")
                    return True
        except Exception as e:
            logger.error(f"设备SDK配置初始化失败: {str(e)}")
            raise Exception(f"SDK初始化失败: {str(e)}")

    async def connect(self):
        try:
            # # 先初始化SDK
            # await self.initialize_sdk()

            # 然后连接设备
            if self.config.protocol_type == ProtocolType.Modbus:
                self.client = await self._sdk.modbus_open(
                    self.config.connect_port,
                    self.config.connect_rs485_baudrate.to_sdk_type()
                )
                self.connected = True
                return self.client
            else:
                raise ValueError(f"不支持的协议类型: {self.config.protocol_type}")
        except Exception as e:
            raise Exception(f"{str(e)}")

    async def disconnect(self):
        if self.client:
            try:
                await self._sdk.modbus_close(self.client)
                self.client = None
                self.connected = False
                return True
            except Exception as e:
                logger.error(f"断开连接失败: {str(e)}")
                raise Exception(f"断开连接失败: {str(e)}")
        return False

    async def execute_command(self, method_name: str, *args):
        if not self.client:
            raise Exception(f"Device not connected")

        try:
            method = getattr(self.client, method_name)
            if not callable(method):
                raise AttributeError(f"方法 {method_name} 不可调用")
            return await method(*args)
        except Exception as e:
            raise Exception(f"Function {method_name} Execution Failed: {str(e)}")


# SDK管理器
class SDKManager(QObject):
    # 信号定义
    connect_result_signal = Signal(bool, str, HandType, str)  # 连接状态, 端口, 手类型, msg
    motor_status_received_signal = Signal(object, HandType)  # 电机状态, 手类型
    finger_unit_mode_received_signal = Signal(object, HandType)  # 控制单元模式, 手类型
    finger_unit_mode_error_signal = Signal(str)  # msg
    error_occurred_signal = Signal(str, HandType)  # 错误信息, 手类型
    get_completed_device_info_signal = Signal(HandDevice)

    # 初始化任务完成信号
    initialization_task_completed_signal = Signal(str, HandType)  # task_name, hand_type

    dfu_started_signal = Signal(str)
    dfu_state_signal = Signal(HandType, stark.DfuState)
    dfu_progress_signal = Signal(HandType, float)
    dfu_error_signal = Signal(HandType, str)
    dfu_finished_signal = Signal(HandType, bool)  # hand_type, success
    dfu_reconnect_signal = Signal(HandType, bool, str)  # hand_type, success, message

    update_urdf_signal = Signal(HandType, list, list)

    _instance = None

    @classmethod
    def instance(cls):
        if cls._instance is None:
            cls._instance = SDKManager()
        return cls._instance

    def __init__(self):
        super().__init__()
        self._runner = AsyncRunner.instance()
        self._hands: Dict[HandType, HandDevice] = {}
        self._dfu_device_configs: Dict[HandType, HandConfig] = {}  # 存储DFU前的设备配置

        self._request_action_sequence_idx = 0

        # 新的统一设备工作器机制
        self._device_command_queues: Dict[HandType, asyncio.Queue] = {}
        self._device_workers: Dict[HandType, asyncio.Task] = {}
        self._device_workers_running: Dict[HandType, bool] = {}
        self._status_monitoring_enabled: Dict[HandType, bool] = {}

    def auto_detect_device(self, port: str):
        async def _detect():
            return await stark.auto_detect_device(port)

        def on_success(result):
            (protocol, port_name, baudrate, slave_id) = result
            logger.info(f"Detected protocol: {protocol}, port: {port_name}, baudrate: {baudrate}, slave_id: {slave_id}")

        def on_error(error_msg):
            logger.error(f"Device detection failed: {error_msg}")

        self._runner.run_async(_detect(), on_success, on_error)

    async def _unified_device_worker(self, hand_type: HandType):
        if hand_type not in self._device_command_queues:
            self._device_command_queues[hand_type] = asyncio.Queue()

        self._device_workers_running[hand_type] = True
        slave_id = self._hands[hand_type].config.slave_address

        while self._device_workers_running.get(hand_type, False) and hand_type in self._hands:
            try:
                try:

                    command_info = await asyncio.wait_for(
                        self._device_command_queues[hand_type].get(),
                        timeout=0.001
                    )

                    method_name, args, result_future, error_future = command_info
                    try:
                        result = await self._hands[hand_type].execute_command(method_name, *args)
                        if result_future:
                            result_future.set_result(result)
                    except Exception as e:
                        if error_future:
                            error_future.set_exception(e)
                        elif self._device_workers_running.get(hand_type, False):
                            self.error_occurred_signal.emit(str(e), hand_type)

                    # 用户命令执行后，立即检查下一个命令
                    continue
                except asyncio.TimeoutError:
                    # 没有用户命令，执行状态监控（如果启用）
                    if self._status_monitoring_enabled.get(hand_type, False):
                        try:
                            start_time = time.time()
                            status = await self._hands[hand_type].execute_command("get_motor_status", slave_id)
                            logger.debug(f"execute_command get_motor_status took {(time.time() - start_time) * 1000:.2f} ms")
                            self._on_motor_status_received(status, hand_type)
                        except Exception as e:
                            # 监视器若报错，不抛出异常，但记录日志
                            logger.error(f"Error during status monitoring for {hand_type}: {e}")
                            # if self._device_workers_running.get(hand_type, False):
                            #     self.error_occurred_signal.emit(str(e), hand_type)
                    else:
                        await asyncio.sleep(0.001)
            except Exception as e:
                if self._device_workers_running.get(hand_type, False):
                    logger.error(f"Device worker error for {hand_type}: {e}")
                await asyncio.sleep(0.01)

        self._device_workers_running[hand_type] = False

    def _start_device_worker(self, hand_type: HandType):
        if self._device_workers_running.get(hand_type, False):
            return  # 已经在运行

        async def start_worker():
            task = asyncio.create_task(self._unified_device_worker(hand_type))
            self._device_workers[hand_type] = task
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"Device worker for {hand_type} was cancelled")
            except Exception as e:
                logger.error(f"Device worker for {hand_type} failed: {e}")
            finally:
                if hand_type in self._device_workers:
                    del self._device_workers[hand_type]
                self._device_workers_running[hand_type] = False

        self._runner.run_async(start_worker())

    def _stop_device_worker(self, hand_type: HandType):
        self._device_workers_running[hand_type] = False

        # 取消任务
        if hand_type in self._device_workers:
            task = self._device_workers[hand_type]
            if not task.done():
                task.cancel()

        # 清空命令队列
        if hand_type in self._device_command_queues:
            while not self._device_command_queues[hand_type].empty():
                try:
                    command_info = self._device_command_queues[hand_type].get_nowait()
                    # 如果有等待的future，设置取消异常
                    _, _, result_future, error_future = command_info
                    if result_future and not result_future.done():
                        result_future.cancel()
                    if error_future and not error_future.done():
                        error_future.cancel()
                except asyncio.QueueEmpty:
                    break

    @require_hand_connected
    def start_motor_status_monitoring(self, hand_type: HandType):
        self._status_monitoring_enabled[hand_type] = True

        # 如果设备工作器还没启动，启动它
        if not self._device_workers_running.get(hand_type, False):
            self._start_device_worker(hand_type)

    def stop_motor_status_monitoring(self, hand_type: HandType):
        self._status_monitoring_enabled[hand_type] = False

    async def _execute_device_command_async(self, hand_type: HandType, method_name: str, *args, wait_for_result=True):
        if hand_type not in self._hands:
            raise Exception(f"{hand_type.name} hand not connected")

        # 确保设备工作器正在运行
        if not self._device_workers_running.get(hand_type, False):
            self._start_device_worker(hand_type)
            # 等待工作器启动
            await asyncio.sleep(0.01)

        # 创建future用于获取结果
        result_future = asyncio.Future() if wait_for_result else None
        error_future = asyncio.Future() if wait_for_result else None

        # 将命令放入队列
        command_info = (method_name, args, result_future, error_future)
        await self._device_command_queues[hand_type].put(command_info)

        if wait_for_result:
            # 等待结果
            try:
                return await result_future
            except Exception as e:
                # 如果result_future没有异常，检查error_future
                if not result_future.done() and error_future.done():
                    try:
                        await error_future
                    except Exception as error_e:
                        raise error_e
                raise e
        else:
            return None

    @require_hand_connected
    def start_dfu(self, hand_type: HandType, dfu_file_path: str):
        # 保存DFU前的设备配置，用于重新连接
        self._dfu_device_configs[hand_type] = self._hands[hand_type].config

        def on_error(error_msg):
            self.dfu_error_signal.emit(hand_type, error_msg)
            # DFU失败后尝试重新连接
            self._schedule_reconnect_after_dfu(hand_type, False)

        def on_sdk_dfu_state(_, state):
            self.dfu_state_signal.emit(hand_type, state)

            # 检查DFU是否完成（成功或失败）
            if state == stark.DfuState.Completed:
                logger.info(f"DFU升级成功完成: {hand_type.name}")
                self.dfu_finished_signal.emit(hand_type, True)
                self._schedule_reconnect_after_dfu(hand_type, True)
            elif state == stark.DfuState.Aborted:
                logger.warning(f"DFU升级被中止: {hand_type.name}")
                self.dfu_finished_signal.emit(hand_type, False)
                self._schedule_reconnect_after_dfu(hand_type, False)
            elif state == stark.DfuState.Starting:
                logger.info(f"DFU升级开始: {hand_type.name}")
                self.dfu_started_signal.emit(dfu_file_path)

        def on_sdk_dfu_progress(_, progress):
            value = round(progress, 2) * 100
            self.dfu_progress_signal.emit(hand_type, value)

        slave_id = self._hands[hand_type].config.slave_address

        async def _execute_async():
            return await self._hands[hand_type].execute_command("start_dfu",
                                                                slave_id,
                                                                dfu_file_path,
                                                                5,
                                                                on_sdk_dfu_state,
                                                                on_sdk_dfu_progress)

        self._runner.run_async(
            _execute_async(),
            on_error=on_error,
        )

    def _schedule_reconnect_after_dfu(self, hand_type: HandType, dfu_success: bool):
        logger.info(f"安排DFU后重新连接: {hand_type.name}, DFU成功: {dfu_success}")

        # 延时后尝试重新连接
        async def _reconnect_after_delay():
            # 等待设备重启完成
            await asyncio.sleep(3)

            # 尝试重新连接
            await self._reconnect_device_after_dfu(hand_type, dfu_success)

        self._runner.run_async(_reconnect_after_delay())

    async def _reconnect_device_after_dfu(self, hand_type: HandType, dfu_success: bool):
        if hand_type not in self._dfu_device_configs:
            logger.error(f"未找到{hand_type.name}手的DFU前配置")
            self.dfu_reconnect_signal.emit(hand_type, False, "未找到设备配置")
            return

        original_config = self._dfu_device_configs[hand_type]
        max_retries = 5
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试重新连接{hand_type.name}手 (第{attempt + 1}次/共{max_retries}次)")

                # 如果设备仍在连接状态，先断开
                if hand_type in self._hands and self._hands[hand_type].connected:
                    logger.info(f"断开{hand_type.name}手的连接")
                    await self._hands[hand_type].disconnect()
                    logger.info(f"断开成功")
                    del self._hands[hand_type]
                    await asyncio.sleep(2)

                # 创建新的设备实例
                new_hand_device = HandDevice(original_config)
                client = await new_hand_device.connect()

                # 验证连接是否成功
                slave_id = original_config.slave_address
                device_info = await new_hand_device.execute_command("get_device_info", slave_id)

                # 连接成功，更新设备列表
                self._hands[hand_type] = new_hand_device

                # 清理DFU配置
                del self._dfu_device_configs[hand_type]

                success_msg = f"Reconnected successfully after DFU"
                if dfu_success:
                    success_msg += " (firmware upgrade successful)"
                else:
                    success_msg += " (firmware upgrade failed, but the device has reconnected)"

                logger.info(f"{hand_type.name}手{success_msg}")
                self.dfu_reconnect_signal.emit(hand_type, True, success_msg)

                self.connect_result_signal.emit(True, original_config.connect_port, hand_type, success_msg)
                return

            except Exception as e:
                error_msg = f"重新连接失败 (第{attempt + 1}次): {str(e)}"
                logger.warning(f"{hand_type.name}手{error_msg}")

                if attempt < max_retries - 1:
                    logger.info(f"等待{retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                else:
                    final_error = f"Reconnection failed after{max_retries}attempts"
                    logger.error(f"{hand_type.name}手{final_error}")
                    self.dfu_reconnect_signal.emit(hand_type, False, final_error)

                    # 清理DFU配置
                    if hand_type in self._dfu_device_configs:
                        del self._dfu_device_configs[hand_type]

    def connect_device(self, hand_config: HandConfig, verify_hand_type=True):
        """

        :param hand_config:
        :param verify_hand_type: 是否需要验证手类型（第一次连接时为True，已知手类型时为False）
        :return:
        :rtype:
        """
        async def _connect_async():
            # 创建新的手设备
            hand_device = HandDevice(hand_config)
            client = await hand_device.connect()

            # 如果需要验证手类型（第一次连接时）
            if verify_hand_type:
                # 获取设备信息
                slave_id = hand_config.slave_address
                device_info = await hand_device.execute_command("get_device_info", slave_id)
                if hand_device.config.hand_version == HandVersion.V2:
                    hand_device.config.serial_number = device_info.serial_number
                    actual_hand_type = HandType.Left if device_info.serial_number[4] == "L" else HandType.Right

                    # 如果传入的手类型与实际不符，更新为正确的手类型
                    if actual_hand_type != hand_config.hand_type:
                        logger.info(f"更新手类型: 从 {hand_config.hand_type.name} 到 {actual_hand_type.name}")
                        hand_device.config.hand_type = actual_hand_type

            # 使用正确的手类型存储设备
            self._hands[hand_config.hand_type] = hand_device
            return client, hand_config.hand_type

        def on_success(result):
            client, hand_type = result
            # 连接成功后启动设备工作器
            self._start_device_worker(hand_type)
            self.connect_result_signal.emit(True, hand_config.connect_port, hand_type, "")
            return client

        def on_error(error_msg):
            # self.errorOccurred.emit(error_msg, hand_config.hand_type)
            self.connect_result_signal.emit(False, hand_config.connect_port, hand_config.hand_type, error_msg)

        # 检查是否已存在相同类型的手
        if hand_config.hand_type in self._hands.keys():
            self.error_occurred_signal.emit(f"Already connected to {hand_config.hand_type.name} hand", hand_config.hand_type)
            return

        # 检查是否连接到同一个ID和端口号
        for device in self._hands.values():
            if hand_config.slave_address == device.config.slave_address:
                self.error_occurred_signal.emit(f"Already connected to the same ID: {hand_config.slave_address}", hand_config.hand_type)
                return
            if hand_config.connect_port == device.config.connect_port:
                self.error_occurred_signal.emit(f"Already connected to the same Port: {hand_config.connect_port}", hand_config.hand_type)
                return

        logger.info(
            f"正在连接: {hand_config.connect_port}, 波特率: {hand_config.connect_rs485_baudrate}")
        self._runner.run_async(_connect_async(), on_success, on_error)

    def disconnect_device(self, hand_type: HandType):
        async def _disconnect_async():
            if hand_type in self._hands:
                # 先停止设备工作器
                self._stop_device_worker(hand_type)

                port = self._hands[hand_type].config.connect_port
                result = await self._hands[hand_type].disconnect()
                if result:
                    del self._hands[hand_type]

                # 清理状态监控标志
                if hand_type in self._status_monitoring_enabled:
                    del self._status_monitoring_enabled[hand_type]

                # 如果设备正在DFU过程中被手动断开，清理DFU配置
                if hand_type in self._dfu_device_configs:
                    logger.info(f"清理{hand_type.name}手的DFU配置（手动断开连接）")
                    del self._dfu_device_configs[hand_type]

                return result, port
            return False, None

        def on_success(result):
            success, port = result
            if success and port:
                # self.connectResult.emit(False, port, hand_type)
                logger.info(f"已断开{hand_type.name}手连接: {port}")
            return success

        def on_error(error_msg):
            self.error_occurred_signal.emit(f"{error_msg}", hand_type)

        self._runner.run_async(_disconnect_async(), on_success, on_error)

    def close(self):
        async def _disconnect_async():
            # 停止所有设备工作器
            for hand_type in list(self._hands.keys()):
                self._stop_device_worker(hand_type)

            for hand_type in self._hands.keys():
                result = await self._hands[hand_type].disconnect()
                logger.warning(f"{hand_type.name} 关闭设备结束；{result}")

            # 清理所有状态
            self._status_monitoring_enabled.clear()

        self._runner.run_async(_disconnect_async())

    def _normalize_position(self, value: int, min_val: int, max_val: int):
        if max_val == min_val:
            return 0.0
        return (value - min_val) / (max_val - min_val)

    @require_hand_connected
    def set_finger_position_with_millis(self, hand_type: HandType, finger_id: FingerID, position: int, milli_second: int):
        config = self._hands[hand_type].config
        if config.unit_mode == stark.FingerUnitMode.Normalized:
            urdf_control_params = [position / 10]
        else:
            min_val = config.min_positions[finger_id.value - 1]
            max_val = config.max_positions[finger_id.value - 1]
            percentage = self._normalize_position(position, min_val, max_val)
            urdf_control_params = [percentage * 10]
        self.update_urdf_signal.emit(hand_type, [finger_id], urdf_control_params)

        slave_id = self._hands[hand_type].config.slave_address
        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_position_with_millis",
                                                   slave_id, finger_id.to_sdk_type(), position, milli_second,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_finger_positions_and_durations(self, hand_type: HandType, finger_positions: List[int], durations: List[int]):
        config = self._hands[hand_type].config
        if config.unit_mode == stark.FingerUnitMode.Normalized:
            urdf_control_params = [pos / 10 for pos in finger_positions]
        else:
            urdf_control_params = [
                self._normalize_position(
                    finger_positions[i],
                    config.min_positions[i],
                    config.max_positions[i]
                ) * 10 for i in range(len(finger_positions))
            ]
        self.update_urdf_signal.emit(hand_type, list(FingerID), urdf_control_params)

        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_positions_and_durations",
                                                   slave_id, finger_positions, durations,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_finger_speeds(self, hand_type: HandType, speeds):
        self.update_urdf_signal.emit(hand_type, [finger_id for finger_id in FingerID], [100, 100, 100, 100, 100, 100])
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_speeds",
                                                   slave_id, speeds,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_finger_currents(self, hand_type: HandType, currents):
        self.update_urdf_signal.emit(hand_type, [finger_id for finger_id in FingerID], [100, 100, 100, 100, 100, 100])
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_currents",
                                                   slave_id, currents,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_finger_positions_and_speeds(self, hand_type: HandType, positions, speeds):
        self.update_urdf_signal.emit(hand_type, [finger_id for finger_id in FingerID], [100, 100, 100, 100, 100, 100])

        # 检查speeds中是否有值为0， 如果是0则替换为1
        if any(speed == 0 for speed in speeds):
            speeds = [1 if speed == 0 else speed for speed in speeds]

        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_positions_and_speeds",
                                                   slave_id, positions, speeds,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_finger_unit_mode(self, hand_type: HandType, unit_mode: stark.FingerUnitMode):
        slave_id = self._hands[hand_type].config.slave_address
        self._hands[hand_type].config.unit_mode = unit_mode  # todo 最好的做法是再成功回调函数设置

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_finger_unit_mode",
                                                   slave_id, unit_mode,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_position_auto_calibration(self, hand_type: HandType, enabled: bool):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_position_auto_calibration",
                                                   slave_id, enabled,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_turbo_mode_enabled(self, hand_type: HandType, enabled: bool):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_turbo_mode_enabled",
                                                   slave_id, enabled,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_led_enabled(self, hand_type: HandType, enabled: bool):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_led_enabled",
                                                   slave_id, enabled,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_buzzer_enabled(self, hand_type: HandType, enabled: bool):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_buzzer_enabled",
                                                   slave_id, enabled,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_vibration_enabled(self, hand_type: HandType, enabled: bool):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "set_vibration_enabled",
                                                   slave_id, enabled,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def set_packing_gestures(self, hand_type: HandType):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute_packing_gestures():
            self._hands[hand_type]._packing_gesture_count = 0

            while self._hands[hand_type]._packing_gesture_count <= 2:
                finger_positions = self._hands[hand_type]._packing_gesture_params[self._hands[hand_type]._packing_gesture_count][0]
                finger_durations = self._hands[hand_type]._packing_gesture_params[self._hands[hand_type]._packing_gesture_count][1]

                await self._execute_device_command_async(hand_type, "set_finger_positions_and_durations",
                                                       slave_id, finger_positions, finger_durations)

                self._hands[hand_type]._packing_gesture_count += 1

                if self._hands[hand_type]._packing_gesture_count <= 2:
                    await asyncio.sleep(2)  # 等待2秒再执行下一个手势

        self._runner.run_async(_execute_packing_gestures())

    @require_hand_connected
    def calibrate_position(self, hand_type: HandType):
        self.update_urdf_signal.emit(hand_type, [finger_id for finger_id in FingerID], [0, 0, 0, 0, 0, 0])
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "calibrate_position",
                                                   slave_id,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def reset_default_settings(self, hand_type: HandType):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "reset_default_settings",
                                                   slave_id,
                                                   wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def get_finger_unit_mode(self, hand_type: HandType):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            try:
                result = await self._execute_device_command_async(hand_type, "get_finger_unit_mode", slave_id)
                self._on_finger_unit_mode_received(result, self._hands[hand_type])
            except Exception as e:
                self._on_finger_unit_mode_error(str(e))

        self._runner.run_async(_execute())

    @require_hand_connected
    def get_all_device_info_for_ui(self, hand_type: HandType):
        hand_device = self._hands[hand_type]

        # Reset arrays
        hand_device.config.min_positions = [None] * 6
        hand_device.config.max_positions = [None] * 6
        hand_device.config.max_speeds = [None] * 6
        hand_device.config.max_currents = [None] * 6

        async def _get_all_info_sequential():
            slave_id = self._hands[hand_type].config.slave_address\

            device_info = await hand_device.execute_command("get_device_info", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_device_info_received(device_info, hand_device)
            self.initialization_task_completed_signal.emit("Getting Device Info", hand_device.config.hand_type)

            auto_cal = await hand_device.execute_command("get_auto_calibration_enabled", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_auto_calibration_received(auto_cal, hand_device)
            self.initialization_task_completed_signal.emit("Getting Auto Calibration Status", hand_device.config.hand_type)

            turbo_mode_enabled = await hand_device.execute_command("get_turbo_mode_enabled", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_turbo_mode_enabled_received(turbo_mode_enabled, hand_device)
            self.initialization_task_completed_signal.emit("Getting Turbo Mode Status", hand_device.config.hand_type)

            led_enabled = await hand_device.execute_command("get_led_enabled", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_led_enabled_received(led_enabled, hand_device)
            self.initialization_task_completed_signal.emit("Getting LED Status", hand_device.config.hand_type)

            buzzer_enabled = await hand_device.execute_command("get_buzzer_enabled", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_buzzer_enabled_received(buzzer_enabled, hand_device)
            self.initialization_task_completed_signal.emit("Getting Buzzer Status", hand_device.config.hand_type)

            vibration_enabled = await hand_device.execute_command("get_vibration_enabled", slave_id)
            await asyncio.sleep(get_info_delay)
            self._on_vibration_enabled_received(vibration_enabled, hand_device)
            self.initialization_task_completed_signal.emit("Getting Vibration Status", hand_device.config.hand_type)

            for finger_id in FingerID:
                min_position = await hand_device.execute_command("get_finger_min_position", slave_id, finger_id.to_sdk_type())
                await asyncio.sleep(get_info_delay)
                self._on_finger_min_position_received(min_position, finger_id, hand_device)
                self.initialization_task_completed_signal.emit("Getting Finger Min Positions", hand_device.config.hand_type)

                max_position = await hand_device.execute_command("get_finger_max_position", slave_id, finger_id.to_sdk_type())
                await asyncio.sleep(get_info_delay)
                self._on_finger_max_position_received(max_position, finger_id, hand_device)
                self.initialization_task_completed_signal.emit("Getting Finger Max Positions", hand_device.config.hand_type)

                max_speed = await hand_device.execute_command("get_finger_max_speed", slave_id, finger_id.to_sdk_type())
                await asyncio.sleep(get_info_delay)
                self._on_finger_max_speed_received(max_speed, finger_id, hand_device)
                self.initialization_task_completed_signal.emit("Getting Finger Max Speeds",hand_device.config.hand_type)

                max_current = await hand_device.execute_command("get_finger_max_current", slave_id, finger_id.to_sdk_type())
                await asyncio.sleep(get_info_delay)
                self._on_finger_max_current_received(max_current, finger_id, hand_device)
                self.initialization_task_completed_signal.emit("Getting Finger Max Currents", hand_device.config.hand_type)

            for action_id in ActionSequenceId:
                result = await hand_device.execute_command("get_action_sequence", slave_id, action_id.to_sdk_type())
                await asyncio.sleep(get_info_delay)
                self._on_action_sequence_received(action_id, result, hand_device)
            self.initialization_task_completed_signal.emit("Getting Action Sequences", hand_device.config.hand_type)

            self.get_completed_device_info_signal.emit(hand_device)

        self._runner.run_async(_get_all_info_sequential())

    @require_hand_connected
    def transfer_action_sequence(self, hand_type: HandType, action_id: ActionSequenceId, sequences: list):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "transfer_action_sequence",
                                                     slave_id, action_id.to_sdk_type(), sequences,
                                                     wait_for_result=False)
        self._runner.run_async(_execute())

    @require_hand_connected
    def run_action_sequence(self, hand_type: HandType, action_id: ActionSequenceId):
        slave_id = self._hands[hand_type].config.slave_address

        async def _execute():
            await self._execute_device_command_async(hand_type, "run_action_sequence",
                                                     slave_id, action_id.to_sdk_type(),
                                                     wait_for_result=False)
        self._runner.run_async(_execute())

    def _on_action_sequence_received(self, action_id: ActionSequenceId, result: stark.ActionSequence, hand_device: HandDevice):
        if len(result.data) > 0:
            hand_device.config.insert_action_sequence(action_id, result)

    def _on_finger_min_position_received(self, min_position: int, finger_id: FingerID, hand_device: HandDevice):
        hand_device.config.min_positions[finger_id.value - 1] = min_position

    def _on_finger_max_position_received(self, max_position: int, finger_id: FingerID, hand_device: HandDevice):
        hand_device.config.max_positions[finger_id.value - 1] = max_position

    def _on_finger_max_speed_received(self, max_speed: int, finger_id: FingerID, hand_device: HandDevice):
        hand_device.config.max_speeds[finger_id.value - 1] = max_speed

    def _on_finger_max_current_received(self, max_current: int, finger_id: FingerID, hand_device: HandDevice):
        hand_device.config.max_currents[finger_id.value - 1] = max_current

    def _on_finger_unit_mode_received(self, unit_mode: stark.FingerUnitMode, hand_device: HandDevice):
        hand_device.config.unit_mode = unit_mode
        self.finger_unit_mode_received_signal.emit(unit_mode, hand_device.config.hand_type)

    def _on_finger_unit_mode_error(self, error_msg):
        self.finger_unit_mode_error_signal.emit(error_msg)

    def _on_auto_calibration_received(self, enabled: bool, hand_device: HandDevice):
        hand_device.config.auto_calibration_enabled = enabled

    def _on_turbo_mode_enabled_received(self, enabled: bool, hand_device: HandDevice):
        hand_device.config.turbo_mode_enabled = enabled

    def _on_led_enabled_received(self, enabled: bool, hand_device: HandDevice):
        hand_device.config.led_enabled = enabled

    def _on_buzzer_enabled_received(self, enabled: bool, hand_device: HandDevice):
        hand_device.config.buzzer_enabled = enabled

    def _on_vibration_enabled_received(self, enabled: bool, hand_device: HandDevice):
        hand_device.config.vibration_enabled = enabled

    def _on_device_info_received(self, device_info, hand_device: HandDevice):
        if hand_device.config.hand_version == HandVersion.V2:
            hand_device.config.serial_number = device_info.serial_number
            hand_device.config.hand_type = HandType.Left if device_info.serial_number[4] == "L" else HandType.Right
            hand_device.config.sku = device_info.serial_number[2:6]
            hand_device.config.firmware_version = device_info.firmware_version

    def _on_motor_status_received(self, status, hand_type):
        self.motor_status_received_signal.emit(status, hand_type)
        return status

    def get_connected_hands(self):
        return self._hands

    def is_hand_connected(self, hand_type: HandType):
        return hand_type in self._hands and self._hands[hand_type].connected

    def clear_device_command_queue(self, hand_type: HandType):
        if hand_type not in self._device_command_queues:
            return

        queue = self._device_command_queues[hand_type]
        cleared_count = 0

        # 清空队列中的所有命令
        while not queue.empty():
            try:
                command_info = queue.get_nowait()
                cleared_count += 1

                # 取消等待中的future对象
                method_name, args, result_future, error_future = command_info
                if result_future and not result_future.done():
                    result_future.cancel()
                if error_future and not error_future.done():
                    error_future.cancel()

            except asyncio.QueueEmpty:
                break

        logger.info(f"已清空 {hand_type.name} 设备的 {cleared_count} 个待处理命令")
        return cleared_count

    def clear_all_device_command_queues(self):
        total_cleared = 0
        for hand_type in self._device_command_queues.keys():
            cleared = self.clear_device_command_queue(hand_type)
            total_cleared += cleared

        return total_cleared



